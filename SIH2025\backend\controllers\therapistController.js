const Therapist = require('../models/Therapist');
const Appointment = require('../models/Appointment');
const Feedback = require('../models/Feedback');
const { generateToken } = require('../middleware/authMiddleware');
const mongoose = require('mongoose');

// @desc    Get all therapists
// @route   GET /api/therapists
// @access  Public (for booking), Admin for full details
const getTherapists = async (req, res) => {
  try {
    const { specialization, isActive = true } = req.query;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const query = { isActive };
    if (specialization) {
      query.specialization = specialization;
    }

    const therapists = await Therapist.find(query)
      .select('-password')
      .populate('therapiesOffered', 'name code')
      .skip(skip)
      .limit(limit)
      .sort({ createdAt: -1 });

    const total = await Therapist.countDocuments(query);

    res.json({
      success: true,
      data: therapists,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get therapists error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

// @desc    Get single therapist
// @route   GET /api/therapists/:id
// @access  Public
const getTherapist = async (req, res) => {
  try {
    const therapist = await Therapist.findById(req.params.id)
      .select('-password')
      .populate('therapiesOffered', 'name code description duration');

    if (!therapist) {
      return res.status(404).json({ error: 'Therapist not found' });
    }

    res.json({
      success: true,
      data: therapist
    });
  } catch (error) {
    console.error('Get therapist error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

// @desc    Create new therapist
// @route   POST /api/therapists
// @access  Admin only
const createTherapist = async (req, res) => {
  try {
    const therapist = await Therapist.create(req.body);
    
    res.status(201).json({
      success: true,
      data: therapist
    });
  } catch (error) {
    console.error('Create therapist error:', error);
    if (error.code === 11000) {
      return res.status(400).json({ error: 'Therapist with this email already exists' });
    }
    res.status(500).json({ error: 'Server error' });
  }
};

// @desc    Update therapist
// @route   PUT /api/therapists/:id
// @access  Therapist owner or Admin
const updateTherapist = async (req, res) => {
  try {
    const allowedFields = [
      'name', 'phone', 'specialization', 'qualifications', 
      'experience', 'availability', 'therapiesOffered', 'profileImage'
    ];

    const updateData = {};
    allowedFields.forEach(field => {
      if (req.body[field] !== undefined) {
        updateData[field] = req.body[field];
      }
    });

    const therapist = await Therapist.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    ).select('-password');

    if (!therapist) {
      return res.status(404).json({ error: 'Therapist not found' });
    }

    res.json({
      success: true,
      data: therapist
    });
  } catch (error) {
    console.error('Update therapist error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

// @desc    Delete therapist (soft delete)
// @route   DELETE /api/therapists/:id
// @access  Admin only
const deleteTherapist = async (req, res) => {
  try {
    const therapist = await Therapist.findByIdAndUpdate(
      req.params.id,
      { isActive: false },
      { new: true }
    );

    if (!therapist) {
      return res.status(404).json({ error: 'Therapist not found' });
    }

    res.json({
      success: true,
      message: 'Therapist deactivated successfully'
    });
  } catch (error) {
    console.error('Delete therapist error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

// @desc    Get therapist dashboard data
// @route   GET /api/therapists/:id/dashboard
// @access  Therapist owner or Admin
const getTherapistDashboard = async (req, res) => {
  try {
    const therapistId = req.params.id;

    // Get today's appointments
    const today = new Date();
    const startOfDay = new Date(today.setHours(0, 0, 0, 0));
    const endOfDay = new Date(today.setHours(23, 59, 59, 999));

    const todayAppointments = await Appointment.find({
      therapist: therapistId,
      scheduledAt: { $gte: startOfDay, $lte: endOfDay }
    })
    .populate('patient', 'name phone')
    .populate('therapy', 'name duration')
    .sort({ scheduledAt: 1 });

    // Get upcoming appointments (next 7 days)
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 7);

    const upcomingAppointments = await Appointment.find({
      therapist: therapistId,
      scheduledAt: { $gte: new Date(), $lte: nextWeek },
      status: { $in: ['scheduled', 'confirmed'] }
    })
    .populate('patient', 'name phone')
    .populate('therapy', 'name duration')
    .sort({ scheduledAt: 1 })
    .limit(10);

    // Get appointment statistics
    const appointmentStats = await Appointment.aggregate([
      { $match: { therapist: mongoose.Types.ObjectId(therapistId) } },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    // Get recent feedback
    const recentFeedback = await Feedback.find({
      therapist: therapistId
    })
    .populate('patient', 'name')
    .populate('therapy', 'name')
    .sort({ createdAt: -1 })
    .limit(5);

    // Calculate average rating
    const ratingStats = await Feedback.aggregate([
      { $match: { therapist: mongoose.Types.ObjectId(therapistId) } },
      {
        $group: {
          _id: null,
          averageRating: { $avg: '$ratings.overall' },
          totalFeedbacks: { $sum: 1 }
        }
      }
    ]);

    res.json({
      success: true,
      data: {
        todayAppointments,
        upcomingAppointments,
        appointmentStats,
        recentFeedback,
        ratingStats: ratingStats[0] || { averageRating: 0, totalFeedbacks: 0 }
      }
    });
  } catch (error) {
    console.error('Get therapist dashboard error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

// @desc    Get therapist appointments
// @route   GET /api/therapists/:id/appointments
// @access  Therapist owner or Admin
const getTherapistAppointments = async (req, res) => {
  try {
    const therapistId = req.params.id;
    const { status, startDate, endDate, page = 1, limit = 10 } = req.query;

    const query = { therapist: therapistId };

    if (status) {
      query.status = status;
    }

    if (startDate || endDate) {
      query.scheduledAt = {};
      if (startDate) query.scheduledAt.$gte = new Date(startDate);
      if (endDate) query.scheduledAt.$lte = new Date(endDate);
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const appointments = await Appointment.find(query)
      .populate('patient', 'name phone email')
      .populate('therapy', 'name duration')
      .populate('room', 'name roomNumber')
      .sort({ scheduledAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Appointment.countDocuments(query);

    res.json({
      success: true,
      data: appointments,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    console.error('Get therapist appointments error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

// @desc    Register new therapist (public registration)
// @route   POST /api/therapists/register
// @access  Public
const registerTherapist = async (req, res) => {
  try {
    const {
      name,
      email,
      phone,
      password,
      specialization,
      experience
    } = req.body;

    // Check if therapist already exists
    const existingTherapist = await Therapist.findOne({ email });
    if (existingTherapist) {
      return res.status(400).json({ error: 'Therapist with this email already exists' });
    }

    // Skip qualifications for now to avoid schema issues

    // Validate and set specialization
    const validSpecializations = [
      'Panchakarma Specialist',
      'Abhyanga Therapist',
      'Shirodhara Specialist',
      'Udvartana Therapist',
      'Nasya Specialist',
      'Basti Therapist',
      'General Ayurvedic Therapist'
    ];

    let validSpecialization = 'General Ayurvedic Therapist'; // default
    if (specialization) {
      // Try to match the input to a valid specialization
      const lowerInput = specialization.toLowerCase();
      const match = validSpecializations.find(spec =>
        spec.toLowerCase().includes(lowerInput) ||
        lowerInput.includes(spec.toLowerCase().split(' ')[0].toLowerCase())
      );
      if (match) {
        validSpecialization = match;
      }
    }

    // Create therapist with default values
    const therapist = await Therapist.create({
      name,
      email,
      phone,
      password,
      role: 'therapist',
      specialization: validSpecialization,
      qualifications: [], // Empty array for now
      experience: experience || 0,
      isActive: true,
      availability: {
        monday: { isAvailable: true, startTime: '09:00', endTime: '17:00' },
        tuesday: { isAvailable: true, startTime: '09:00', endTime: '17:00' },
        wednesday: { isAvailable: true, startTime: '09:00', endTime: '17:00' },
        thursday: { isAvailable: true, startTime: '09:00', endTime: '17:00' },
        friday: { isAvailable: true, startTime: '09:00', endTime: '17:00' },
        saturday: { isAvailable: true, startTime: '09:00', endTime: '14:00' },
        sunday: { isAvailable: false, startTime: '09:00', endTime: '17:00' }
      }
    });

    // Generate token
    const token = generateToken(therapist._id, 'therapist');

    // Remove password from response
    therapist.password = undefined;

    res.status(201).json({
      success: true,
      token,
      user: {
        id: therapist._id,
        name: therapist.name,
        email: therapist.email,
        role: 'therapist',
        phone: therapist.phone,
        specialization: therapist.specialization
      }
    });
  } catch (error) {
    console.error('Register therapist error:', error);
    if (error.code === 11000) {
      return res.status(400).json({ error: 'Therapist with this email already exists' });
    }
    res.status(500).json({ error: 'Server error during registration' });
  }
};

// Get therapist dashboard data (alias for frontend compatibility)
const getDashboard = async (req, res) => {
  try {
    const therapistId = req.params.id;
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // Get today's appointments
    const todayAppointments = await Appointment.find({
      therapist: therapistId,
      scheduledAt: { $gte: today, $lt: tomorrow }
    })
    .populate('patient', 'name email phone')
    .populate('therapy', 'name category')
    .sort({ scheduledAt: 1 });

    // Get upcoming appointments (next 7 days)
    const nextWeek = new Date(today);
    nextWeek.setDate(nextWeek.getDate() + 7);

    const upcomingAppointments = await Appointment.find({
      therapist: therapistId,
      scheduledAt: { $gte: tomorrow, $lt: nextWeek }
    })
    .populate('patient', 'name email phone')
    .populate('therapy', 'name category')
    .sort({ scheduledAt: 1 });

    // Get appointment statistics
    const appointmentStats = await Appointment.aggregate([
      { $match: { therapist: mongoose.Types.ObjectId(therapistId) } },
      { $group: { _id: '$status', count: { $sum: 1 } } }
    ]);

    // Get recent feedback
    const recentFeedback = await Feedback.find({
      therapist: therapistId
    })
    .populate('patient', 'name')
    .populate('therapy', 'name')
    .sort({ createdAt: -1 })
    .limit(10);

    // Calculate rating statistics
    const ratingStats = await Feedback.aggregate([
      { $match: { therapist: mongoose.Types.ObjectId(therapistId) } },
      {
        $group: {
          _id: null,
          averageRating: { $avg: '$ratings.overall' },
          totalFeedbacks: { $sum: 1 }
        }
      }
    ]);

    res.json({
      success: true,
      data: {
        todayAppointments,
        upcomingAppointments,
        appointmentStats,
        recentFeedback,
        ratingStats: ratingStats[0] || { averageRating: 0, totalFeedbacks: 0 }
      }
    });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
};

module.exports = {
  getTherapists,
  getTherapist,
  createTherapist,
  updateTherapist,
  deleteTherapist,
  getTherapistDashboard,
  getTherapistAppointments,
  getDashboard,
  registerTherapist
};
