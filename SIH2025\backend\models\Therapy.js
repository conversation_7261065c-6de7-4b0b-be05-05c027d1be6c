const mongoose = require('mongoose');

const therapySchema = new mongoose.Schema({
  code: {
    type: String,
    required: [true, 'Therapy code is required'],
    unique: true,
    uppercase: true,
    trim: true
  },
  name: {
    type: String,
    required: [true, 'Therapy name is required'],
    trim: true
  },
  description: {
    type: String,
    required: [true, 'Therapy description is required']
  },
  category: {
    type: String,
    required: [true, 'Category is required'],
    enum: [
      'Panchakarma',
      'Abhyanga',
      'Shirodhara',
      'Udvartana',
      'Nasya',
      '<PERSON>sti',
      '<PERSON><PERSON><PERSON><PERSON>',
      'Vamana',
      'Raktamokshana',
      'Other'
    ]
  },
  duration: {
    type: Number,
    required: [true, 'Duration is required'],
    min: 15,
    max: 240 // in minutes
  },
  benefits: [{
    type: String,
    trim: true
  }],
  indications: [{
    type: String,
    trim: true
  }],
  contraindications: [{
    type: String,
    trim: true
  }],
  preparationInstructions: {
    type: String,
    default: ''
  },
  postTreatmentCare: {
    type: String,
    default: ''
  },
  materials: [{
    name: String,
    quantity: String,
    unit: String
  }],
  price: {
    type: Number,
    required: [true, 'Price is required'],
    min: 0
  },
  isActive: {
    type: Boolean,
    default: true
  },
  imageUrl: {
    type: String,
    default: ''
  }
}, {
  timestamps: true
});

// Index for searching
therapySchema.index({ name: 'text', description: 'text' });

module.exports = mongoose.model('Therapy', therapySchema);
