const Patient = require('../models/Patient');
const Appointment = require('../models/Appointment');
const Feedback = require('../models/Feedback');
const { generateToken } = require('../middleware/authMiddleware');

// @desc    Get all patients
// @route   GET /api/patients
// @access  Admin only
const getPatients = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const patients = await Patient.find({ isActive: true })
      .select('-password')
      .skip(skip)
      .limit(limit)
      .sort({ createdAt: -1 });

    const total = await Patient.countDocuments({ isActive: true });

    res.json({
      success: true,
      data: patients,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get patients error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

// @desc    Get single patient
// @route   GET /api/patients/:id
// @access  Patient owner or Admin
const getPatient = async (req, res) => {
  try {
    const patient = await Patient.findById(req.params.id).select('-password');

    if (!patient) {
      return res.status(404).json({ error: 'Patient not found' });
    }

    res.json({
      success: true,
      data: patient
    });
  } catch (error) {
    console.error('Get patient error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

// @desc    Update patient profile
// @route   PUT /api/patients/:id
// @access  Patient owner or Admin
const updatePatient = async (req, res) => {
  try {
    const allowedFields = [
      'name', 'phone', 'notificationPrefs', 'medicalHistory', 
      'prakriti', 'allergies', 'contraindications', 'dateOfBirth', 
      'gender', 'address', 'emergencyContact'
    ];

    const updateData = {};
    allowedFields.forEach(field => {
      if (req.body[field] !== undefined) {
        updateData[field] = req.body[field];
      }
    });

    const patient = await Patient.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    ).select('-password');

    if (!patient) {
      return res.status(404).json({ error: 'Patient not found' });
    }

    res.json({
      success: true,
      data: patient
    });
  } catch (error) {
    console.error('Update patient error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

// @desc    Delete patient (soft delete)
// @route   DELETE /api/patients/:id
// @access  Admin only
const deletePatient = async (req, res) => {
  try {
    const patient = await Patient.findByIdAndUpdate(
      req.params.id,
      { isActive: false },
      { new: true }
    );

    if (!patient) {
      return res.status(404).json({ error: 'Patient not found' });
    }

    res.json({
      success: true,
      message: 'Patient deactivated successfully'
    });
  } catch (error) {
    console.error('Delete patient error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

// @desc    Get patient dashboard data
// @route   GET /api/patients/:id/dashboard
// @access  Patient owner or Admin
const getPatientDashboard = async (req, res) => {
  try {
    const patientId = req.params.id;

    // Get upcoming appointments
    const upcomingAppointments = await Appointment.find({
      patient: patientId,
      scheduledAt: { $gte: new Date() },
      status: { $in: ['scheduled', 'confirmed'] }
    })
    .populate('therapist', 'name specialization')
    .populate('therapy', 'name duration')
    .sort({ scheduledAt: 1 })
    .limit(5);

    // Get recent appointments
    const recentAppointments = await Appointment.find({
      patient: patientId,
      status: 'completed'
    })
    .populate('therapist', 'name specialization')
    .populate('therapy', 'name')
    .sort({ scheduledAt: -1 })
    .limit(5);

    // Get appointment statistics
    const appointmentStats = await Appointment.aggregate([
      { $match: { patient: patientId } },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    // Get recent feedback
    const recentFeedback = await Feedback.find({
      patient: patientId
    })
    .populate('therapy', 'name')
    .sort({ createdAt: -1 })
    .limit(3);

    res.json({
      success: true,
      data: {
        upcomingAppointments,
        recentAppointments,
        appointmentStats,
        recentFeedback
      }
    });
  } catch (error) {
    console.error('Get patient dashboard error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

// @desc    Get patient appointments
// @route   GET /api/patients/:id/appointments
// @access  Patient owner or Admin
const getPatientAppointments = async (req, res) => {
  try {
    const patientId = req.params.id;
    const { status, startDate, endDate, page = 1, limit = 10 } = req.query;

    const query = { patient: patientId };

    if (status) {
      query.status = status;
    }

    if (startDate || endDate) {
      query.scheduledAt = {};
      if (startDate) query.scheduledAt.$gte = new Date(startDate);
      if (endDate) query.scheduledAt.$lte = new Date(endDate);
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const appointments = await Appointment.find(query)
      .populate('therapist', 'name specialization')
      .populate('therapy', 'name duration')
      .populate('room', 'name roomNumber')
      .sort({ scheduledAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Appointment.countDocuments(query);

    res.json({
      success: true,
      data: appointments,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    console.error('Get patient appointments error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

module.exports = {
  getPatients,
  getPatient,
  updatePatient,
  deletePatient,
  getPatientDashboard,
  getPatientAppointments
};
