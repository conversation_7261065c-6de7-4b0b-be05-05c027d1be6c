#!/usr/bin/env node

var path = require("path"),
    bcrypt = require(path.join(__dirname, '..', 'index.js')),
    pkg = require(path.join(__dirname, '..', 'package.json'));

if (process.argv.length < 3) {
    process.stderr.write([ // No dependencies, so we do it from hand.
        "",
        " |_  _ _   _ |_",
        " |_)(_| \\/|_)|_ v"+pkg['version']+" (c) "+pkg['author'],
        "        / |     "
    ].join('\n')+'\n\n'+" Usage: "+path.basename(process.argv[1])+" <input> [rounds|salt]\n");
    process.exit(1);
} else {
    var salt;
    if (process.argv.length > 3) {
        salt = process.argv[3];
        var rounds = parseInt(salt, 10);
        if (rounds == salt)
            salt = bcrypt.genSaltSync(rounds);
    } else
        salt = bcrypt.genSaltSync();
    process.stdout.write(bcrypt.hashSync(process.argv[2], salt)+"\n");
}
