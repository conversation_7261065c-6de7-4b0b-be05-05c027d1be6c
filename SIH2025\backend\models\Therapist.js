const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const therapistSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Name is required'],
    trim: true
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    trim: true
  },
  phone: {
    type: String,
    required: [true, 'Phone number is required'],
    trim: true
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: 6
  },
  role: {
    type: String,
    default: 'therapist',
    enum: ['therapist', 'admin']
  },
  specialization: {
    type: String,
    required: [true, 'Specialization is required'],
    enum: [
      'Panchakarma Specialist',
      'Abhyanga Therapist',
      'Shirodhara Specialist',
      'Udvartana Therapist',
      'Nasya Specialist',
      'Basti Therapist',
      'General Ayurvedic Therapist'
    ]
  },
  qualifications: [String],
  experience: {
    type: Number,
    required: [true, 'Experience is required'],
    min: 0
  },
  availability: [{
    day: {
      type: String,
      enum: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
    },
    startTime: String,
    endTime: String,
    isAvailable: {
      type: Boolean,
      default: true
    }
  }],
  therapiesOffered: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Therapy'
  }],
  rating: {
    average: {
      type: Number,
      default: 0,
      min: 0,
      max: 5
    },
    count: {
      type: Number,
      default: 0
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  profileImage: {
    type: String,
    default: ''
  }
}, {
  timestamps: true
});

// Hash password before saving
therapistSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Compare password method
therapistSchema.methods.comparePassword = async function(candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.password);
};

// Remove password from JSON output
therapistSchema.methods.toJSON = function() {
  const therapist = this.toObject();
  delete therapist.password;
  return therapist;
};

module.exports = mongoose.model('Therapist', therapistSchema);
