const Protocol = require('../models/Protocol');
const Therapy = require('../models/Therapy');

// @desc    Get all protocols
// @route   GET /api/protocols
// @access  Public
const getProtocols = async (req, res) => {
  try {
    const { therapy, difficulty, isActive = true } = req.query;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const query = { isActive };

    if (therapy) {
      query.therapy = therapy;
    }

    if (difficulty) {
      query.difficulty = difficulty;
    }

    const protocols = await Protocol.find(query)
      .populate('therapy', 'name code category')
      .populate('createdBy', 'name specialization')
      .skip(skip)
      .limit(limit)
      .sort({ createdAt: -1 });

    const total = await Protocol.countDocuments(query);

    res.json({
      success: true,
      data: protocols,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get protocols error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

// @desc    Get single protocol
// @route   GET /api/protocols/:id
// @access  Public
const getProtocol = async (req, res) => {
  try {
    const protocol = await Protocol.findById(req.params.id)
      .populate('therapy', 'name code category description')
      .populate('createdBy', 'name specialization');

    if (!protocol) {
      return res.status(404).json({ error: 'Protocol not found' });
    }

    res.json({
      success: true,
      data: protocol
    });
  } catch (error) {
    console.error('Get protocol error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

// @desc    Create new protocol
// @route   POST /api/protocols
// @access  Therapist or Admin
const createProtocol = async (req, res) => {
  try {
    // Verify therapy exists
    const therapy = await Therapy.findById(req.body.therapy);
    if (!therapy) {
      return res.status(404).json({ error: 'Therapy not found' });
    }

    // Add creator information
    req.body.createdBy = req.user._id;

    const protocol = await Protocol.create(req.body);
    
    await protocol.populate('therapy', 'name code category');
    await protocol.populate('createdBy', 'name specialization');

    res.status(201).json({
      success: true,
      data: protocol
    });
  } catch (error) {
    console.error('Create protocol error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

// @desc    Update protocol
// @route   PUT /api/protocols/:id
// @access  Protocol creator or Admin
const updateProtocol = async (req, res) => {
  try {
    const protocol = await Protocol.findById(req.params.id);

    if (!protocol) {
      return res.status(404).json({ error: 'Protocol not found' });
    }

    // Check if user is the creator or admin
    if (req.user.role !== 'admin' && protocol.createdBy.toString() !== req.user._id.toString()) {
      return res.status(403).json({ error: 'Not authorized to update this protocol' });
    }

    const updatedProtocol = await Protocol.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    )
    .populate('therapy', 'name code category')
    .populate('createdBy', 'name specialization');

    res.json({
      success: true,
      data: updatedProtocol
    });
  } catch (error) {
    console.error('Update protocol error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

// @desc    Delete protocol (soft delete)
// @route   DELETE /api/protocols/:id
// @access  Protocol creator or Admin
const deleteProtocol = async (req, res) => {
  try {
    const protocol = await Protocol.findById(req.params.id);

    if (!protocol) {
      return res.status(404).json({ error: 'Protocol not found' });
    }

    // Check if user is the creator or admin
    if (req.user.role !== 'admin' && protocol.createdBy.toString() !== req.user._id.toString()) {
      return res.status(403).json({ error: 'Not authorized to delete this protocol' });
    }

    await Protocol.findByIdAndUpdate(
      req.params.id,
      { isActive: false },
      { new: true }
    );

    res.json({
      success: true,
      message: 'Protocol deactivated successfully'
    });
  } catch (error) {
    console.error('Delete protocol error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

// @desc    Get protocols by therapy
// @route   GET /api/protocols/therapy/:therapyId
// @access  Public
const getProtocolsByTherapy = async (req, res) => {
  try {
    const protocols = await Protocol.find({
      therapy: req.params.therapyId,
      isActive: true
    })
    .populate('therapy', 'name code')
    .populate('createdBy', 'name specialization')
    .sort({ difficulty: 1, createdAt: -1 });

    res.json({
      success: true,
      data: protocols
    });
  } catch (error) {
    console.error('Get protocols by therapy error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

module.exports = {
  getProtocols,
  getProtocol,
  createProtocol,
  updateProtocol,
  deleteProtocol,
  getProtocolsByTherapy
};
