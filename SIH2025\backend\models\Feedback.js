const mongoose = require('mongoose');

const feedbackSchema = new mongoose.Schema({
  appointment: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Appointment',
    required: [true, 'Appointment reference is required']
  },
  patient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient',
    required: [true, 'Patient reference is required']
  },
  therapist: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Therapist',
    required: [true, 'Therapist reference is required']
  },
  therapy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Therapy',
    required: [true, 'Therapy reference is required']
  },
  ratings: {
    overall: {
      type: Number,
      required: [true, 'Overall rating is required'],
      min: 1,
      max: 5
    },
    therapistSkill: {
      type: Number,
      min: 1,
      max: 5
    },
    facilityCleaniness: {
      type: Number,
      min: 1,
      max: 5
    },
    communication: {
      type: Number,
      min: 1,
      max: 5
    },
    comfort: {
      type: Number,
      min: 1,
      max: 5
    },
    effectiveness: {
      type: Number,
      min: 1,
      max: 5
    }
  },
  responses: {
    painLevel: {
      before: {
        type: Number,
        min: 0,
        max: 10
      },
      after: {
        type: Number,
        min: 0,
        max: 10
      }
    },
    energyLevel: {
      before: {
        type: Number,
        min: 0,
        max: 10
      },
      after: {
        type: Number,
        min: 0,
        max: 10
      }
    },
    stressLevel: {
      before: {
        type: Number,
        min: 0,
        max: 10
      },
      after: {
        type: Number,
        min: 0,
        max: 10
      }
    },
    sleepQuality: {
      type: String,
      enum: ['Poor', 'Fair', 'Good', 'Very Good', 'Excellent']
    },
    appetiteChange: {
      type: String,
      enum: ['Decreased', 'No Change', 'Increased']
    },
    moodImprovement: {
      type: String,
      enum: ['Much Worse', 'Worse', 'No Change', 'Better', 'Much Better']
    }
  },
  comments: {
    positive: {
      type: String,
      default: ''
    },
    negative: {
      type: String,
      default: ''
    },
    suggestions: {
      type: String,
      default: ''
    },
    additionalComments: {
      type: String,
      default: ''
    }
  },
  sideEffects: [{
    type: String,
    trim: true
  }],
  wouldRecommend: {
    type: Boolean,
    required: [true, 'Recommendation status is required']
  },
  severityFlag: {
    type: Boolean,
    default: false
  },
  flagReason: {
    type: String,
    default: ''
  },
  isAnonymous: {
    type: Boolean,
    default: false
  },
  status: {
    type: String,
    enum: ['pending', 'submitted', 'reviewed', 'addressed'],
    default: 'submitted'
  },
  reviewedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Therapist'
  },
  reviewedAt: {
    type: Date
  },
  adminResponse: {
    type: String,
    default: ''
  }
}, {
  timestamps: true
});

// Index for efficient queries
feedbackSchema.index({ appointment: 1 });
feedbackSchema.index({ patient: 1, createdAt: -1 });
feedbackSchema.index({ therapist: 1, createdAt: -1 });
feedbackSchema.index({ 'ratings.overall': 1 });

// Pre-save middleware to set severity flag
feedbackSchema.pre('save', function(next) {
  // Flag as severe if overall rating is 2 or below, or if there are serious side effects
  if (this.ratings.overall <= 2 || 
      (this.sideEffects && this.sideEffects.length > 0) ||
      (this.responses.painLevel && this.responses.painLevel.after > this.responses.painLevel.before + 3)) {
    this.severityFlag = true;
    this.flagReason = 'Low rating or potential adverse effects detected';
  }
  next();
});

// Method to calculate improvement scores
feedbackSchema.methods.getImprovementScores = function() {
  const scores = {};
  
  if (this.responses.painLevel && this.responses.painLevel.before && this.responses.painLevel.after) {
    scores.painImprovement = this.responses.painLevel.before - this.responses.painLevel.after;
  }
  
  if (this.responses.energyLevel && this.responses.energyLevel.before && this.responses.energyLevel.after) {
    scores.energyImprovement = this.responses.energyLevel.after - this.responses.energyLevel.before;
  }
  
  if (this.responses.stressLevel && this.responses.stressLevel.before && this.responses.stressLevel.after) {
    scores.stressImprovement = this.responses.stressLevel.before - this.responses.stressLevel.after;
  }
  
  return scores;
};

module.exports = mongoose.model('Feedback', feedbackSchema);
