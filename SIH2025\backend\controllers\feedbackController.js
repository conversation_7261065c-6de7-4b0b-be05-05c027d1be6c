const Feedback = require('../models/Feedback');
const Appointment = require('../models/Appointment');
const mongoose = require('mongoose');

// @desc    Get all feedback
// @route   GET /api/feedback
// @access  Admin, Therapist (own), Patient (own)
const getFeedback = async (req, res) => {
  try {
    const { patient, therapist, therapy, severityFlag } = req.query;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const query = {};

    // Role-based filtering
    if (req.user.role === 'patient') {
      query.patient = req.user._id;
    } else if (req.user.role === 'therapist') {
      query.therapist = req.user._id;
    }

    // Additional filters for admin
    if (req.user.role === 'admin') {
      if (patient) query.patient = patient;
      if (therapist) query.therapist = therapist;
      if (therapy) query.therapy = therapy;
      if (severityFlag !== undefined) query.severityFlag = severityFlag === 'true';
    }

    const feedback = await Feedback.find(query)
      .populate('patient', 'name email')
      .populate('therapist', 'name specialization')
      .populate('therapy', 'name code')
      .populate('appointment', 'scheduledAt protocolStep')
      .skip(skip)
      .limit(limit)
      .sort({ createdAt: -1 });

    const total = await Feedback.countDocuments(query);

    res.json({
      success: true,
      data: feedback,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get feedback error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

// @desc    Get single feedback
// @route   GET /api/feedback/:id
// @access  Admin, Therapist (own), Patient (own)
const getFeedbackById = async (req, res) => {
  try {
    const feedback = await Feedback.findById(req.params.id)
      .populate('patient', 'name email phone')
      .populate('therapist', 'name specialization')
      .populate('therapy', 'name code description')
      .populate('appointment', 'scheduledAt protocolStep duration');

    if (!feedback) {
      return res.status(404).json({ error: 'Feedback not found' });
    }

    // Check authorization
    if (req.user.role === 'patient' && feedback.patient._id.toString() !== req.user._id.toString()) {
      return res.status(403).json({ error: 'Not authorized to view this feedback' });
    }
    if (req.user.role === 'therapist' && feedback.therapist._id.toString() !== req.user._id.toString()) {
      return res.status(403).json({ error: 'Not authorized to view this feedback' });
    }

    res.json({
      success: true,
      data: feedback
    });
  } catch (error) {
    console.error('Get feedback by ID error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

// @desc    Create feedback
// @route   POST /api/feedback
// @access  Patient
const createFeedback = async (req, res) => {
  try {
    const { appointmentId } = req.body;

    // Verify appointment exists and belongs to the patient
    const appointment = await Appointment.findById(appointmentId)
      .populate('therapist', '_id')
      .populate('therapy', '_id');

    if (!appointment) {
      return res.status(404).json({ error: 'Appointment not found' });
    }

    if (appointment.patient.toString() !== req.user._id.toString()) {
      return res.status(403).json({ error: 'Not authorized to provide feedback for this appointment' });
    }

    if (appointment.status !== 'completed') {
      return res.status(400).json({ error: 'Can only provide feedback for completed appointments' });
    }

    // Check if feedback already exists for this appointment
    const existingFeedback = await Feedback.findOne({ appointment: appointmentId });
    if (existingFeedback) {
      return res.status(400).json({ error: 'Feedback already exists for this appointment' });
    }

    // Create feedback
    const feedback = await Feedback.create({
      appointment: appointmentId,
      patient: req.user._id,
      therapist: appointment.therapist._id,
      therapy: appointment.therapy._id,
      ...req.body
    });

    await feedback.populate('therapist', 'name specialization');
    await feedback.populate('therapy', 'name code');
    await feedback.populate('appointment', 'scheduledAt protocolStep');

    // Update therapist rating
    await updateTherapistRating(appointment.therapist._id);

    res.status(201).json({
      success: true,
      data: feedback
    });
  } catch (error) {
    console.error('Create feedback error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

// @desc    Update feedback
// @route   PUT /api/feedback/:id
// @access  Patient (own), Admin
const updateFeedback = async (req, res) => {
  try {
    const feedback = await Feedback.findById(req.params.id);

    if (!feedback) {
      return res.status(404).json({ error: 'Feedback not found' });
    }

    // Check authorization
    if (req.user.role === 'patient' && feedback.patient.toString() !== req.user._id.toString()) {
      return res.status(403).json({ error: 'Not authorized to update this feedback' });
    }

    // Define allowed fields based on role
    let allowedFields = [];
    if (req.user.role === 'admin') {
      allowedFields = ['status', 'reviewedBy', 'adminResponse', 'severityFlag', 'flagReason'];
    } else if (req.user.role === 'patient') {
      allowedFields = ['ratings', 'responses', 'comments', 'sideEffects', 'wouldRecommend', 'isAnonymous'];
    }

    const updateData = {};
    allowedFields.forEach(field => {
      if (req.body[field] !== undefined) {
        updateData[field] = req.body[field];
      }
    });

    // Add review information for admin updates
    if (req.user.role === 'admin' && req.body.status === 'reviewed') {
      updateData.reviewedBy = req.user._id;
      updateData.reviewedAt = new Date();
    }

    const updatedFeedback = await Feedback.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    )
    .populate('patient', 'name email')
    .populate('therapist', 'name specialization')
    .populate('therapy', 'name code')
    .populate('appointment', 'scheduledAt protocolStep');

    // Update therapist rating if ratings were changed
    if (req.body.ratings && req.user.role === 'patient') {
      await updateTherapistRating(feedback.therapist);
    }

    res.json({
      success: true,
      data: updatedFeedback
    });
  } catch (error) {
    console.error('Update feedback error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

// @desc    Delete feedback
// @route   DELETE /api/feedback/:id
// @access  Admin only
const deleteFeedback = async (req, res) => {
  try {
    const feedback = await Feedback.findById(req.params.id);

    if (!feedback) {
      return res.status(404).json({ error: 'Feedback not found' });
    }

    await Feedback.findByIdAndDelete(req.params.id);

    // Update therapist rating after deletion
    await updateTherapistRating(feedback.therapist);

    res.json({
      success: true,
      message: 'Feedback deleted successfully'
    });
  } catch (error) {
    console.error('Delete feedback error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

// @desc    Get feedback statistics
// @route   GET /api/feedback/stats
// @access  Admin, Therapist (own stats)
const getFeedbackStats = async (req, res) => {
  try {
    const { therapist, startDate, endDate } = req.query;
    
    const matchQuery = {};
    
    // Role-based filtering
    if (req.user.role === 'therapist') {
      matchQuery.therapist = mongoose.Types.ObjectId(req.user._id);
    } else if (therapist && req.user.role === 'admin') {
      matchQuery.therapist = mongoose.Types.ObjectId(therapist);
    }

    // Date filtering
    if (startDate || endDate) {
      matchQuery.createdAt = {};
      if (startDate) matchQuery.createdAt.$gte = new Date(startDate);
      if (endDate) matchQuery.createdAt.$lte = new Date(endDate);
    }

    const stats = await Feedback.aggregate([
      { $match: matchQuery },
      {
        $group: {
          _id: null,
          totalFeedbacks: { $sum: 1 },
          averageOverallRating: { $avg: '$ratings.overall' },
          averageTherapistSkill: { $avg: '$ratings.therapistSkill' },
          averageFacilityCleaniness: { $avg: '$ratings.facilityCleaniness' },
          averageCommunication: { $avg: '$ratings.communication' },
          averageComfort: { $avg: '$ratings.comfort' },
          averageEffectiveness: { $avg: '$ratings.effectiveness' },
          recommendationRate: { 
            $avg: { 
              $cond: [{ $eq: ['$wouldRecommend', true] }, 1, 0] 
            } 
          },
          severityFlags: { 
            $sum: { 
              $cond: [{ $eq: ['$severityFlag', true] }, 1, 0] 
            } 
          }
        }
      }
    ]);

    // Get rating distribution
    const ratingDistribution = await Feedback.aggregate([
      { $match: matchQuery },
      {
        $group: {
          _id: '$ratings.overall',
          count: { $sum: 1 }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    res.json({
      success: true,
      data: {
        summary: stats[0] || {
          totalFeedbacks: 0,
          averageOverallRating: 0,
          recommendationRate: 0,
          severityFlags: 0
        },
        ratingDistribution
      }
    });
  } catch (error) {
    console.error('Get feedback stats error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

// Helper function to update therapist rating
const updateTherapistRating = async (therapistId) => {
  try {
    const Therapist = require('../models/Therapist');
    
    const ratingStats = await Feedback.aggregate([
      { $match: { therapist: mongoose.Types.ObjectId(therapistId) } },
      {
        $group: {
          _id: null,
          averageRating: { $avg: '$ratings.overall' },
          totalRatings: { $sum: 1 }
        }
      }
    ]);

    if (ratingStats.length > 0) {
      await Therapist.findByIdAndUpdate(therapistId, {
        'rating.average': Math.round(ratingStats[0].averageRating * 10) / 10,
        'rating.count': ratingStats[0].totalRatings
      });
    }
  } catch (error) {
    console.error('Update therapist rating error:', error);
  }
};

module.exports = {
  getFeedback,
  getFeedbackById,
  createFeedback,
  updateFeedback,
  deleteFeedback,
  getFeedbackStats
};
