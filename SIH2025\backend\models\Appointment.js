const mongoose = require('mongoose');

const appointmentSchema = new mongoose.Schema({
  patient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient',
    required: [true, 'Patient reference is required']
  },
  therapist: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Therapist',
    required: [true, 'Therapist reference is required']
  },
  therapy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Therapy',
    required: [true, 'Therapy reference is required']
  },
  protocol: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Protocol'
  },
  protocolStep: {
    type: String,
    required: [true, 'Protocol step is required']
  },
  stepNumber: {
    type: Number,
    required: [true, 'Step number is required']
  },
  scheduledAt: {
    type: Date,
    required: [true, 'Scheduled time is required']
  },
  duration: {
    type: Number,
    required: [true, 'Duration is required'],
    min: 15,
    max: 240 // in minutes
  },
  status: {
    type: String,
    enum: ['scheduled', 'confirmed', 'in-progress', 'completed', 'cancelled', 'no-show'],
    default: 'scheduled'
  },
  room: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Room'
  },
  notes: {
    therapistNotes: {
      type: String,
      default: ''
    },
    patientNotes: {
      type: String,
      default: ''
    },
    adminNotes: {
      type: String,
      default: ''
    }
  },
  preInstructions: {
    type: String,
    default: ''
  },
  postInstructions: {
    type: String,
    default: ''
  },
  actualStartTime: {
    type: Date
  },
  actualEndTime: {
    type: Date
  },
  actualDuration: {
    type: Number // in minutes
  },
  price: {
    type: Number,
    required: [true, 'Price is required'],
    min: 0
  },
  paymentStatus: {
    type: String,
    enum: ['pending', 'paid', 'partially-paid', 'refunded'],
    default: 'pending'
  },
  cancellationReason: {
    type: String,
    default: ''
  },
  rescheduledFrom: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Appointment'
  },
  isFollowUpRequired: {
    type: Boolean,
    default: false
  },
  followUpDate: {
    type: Date
  }
}, {
  timestamps: true
});

// Index for efficient queries
appointmentSchema.index({ patient: 1, scheduledAt: 1 });
appointmentSchema.index({ therapist: 1, scheduledAt: 1 });
appointmentSchema.index({ status: 1, scheduledAt: 1 });

// Virtual for appointment end time
appointmentSchema.virtual('endTime').get(function() {
  return new Date(this.scheduledAt.getTime() + (this.duration * 60000));
});

// Method to check if appointment is upcoming
appointmentSchema.methods.isUpcoming = function() {
  return this.scheduledAt > new Date() && this.status === 'scheduled';
};

// Method to check if appointment is overdue
appointmentSchema.methods.isOverdue = function() {
  return this.scheduledAt < new Date() && this.status === 'scheduled';
};

module.exports = mongoose.model('Appointment', appointmentSchema);
