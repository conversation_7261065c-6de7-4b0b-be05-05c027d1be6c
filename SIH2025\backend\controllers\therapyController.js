const Therapy = require('../models/Therapy');

// @desc    Get all therapies
// @route   GET /api/therapies
// @access  Public
const getTherapies = async (req, res) => {
  try {
    const { category, isActive = true, search } = req.query;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const query = { isActive };

    if (category) {
      query.category = category;
    }

    if (search) {
      query.$text = { $search: search };
    }

    const therapies = await Therapy.find(query)
      .skip(skip)
      .limit(limit)
      .sort({ createdAt: -1 });

    const total = await Therapy.countDocuments(query);

    res.json({
      success: true,
      data: therapies,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get therapies error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

// @desc    Get single therapy
// @route   GET /api/therapies/:id
// @access  Public
const getTherapy = async (req, res) => {
  try {
    const therapy = await Therapy.findById(req.params.id);

    if (!therapy) {
      return res.status(404).json({ error: 'Therapy not found' });
    }

    res.json({
      success: true,
      data: therapy
    });
  } catch (error) {
    console.error('Get therapy error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

// @desc    Create new therapy
// @route   POST /api/therapies
// @access  Admin only
const createTherapy = async (req, res) => {
  try {
    const therapy = await Therapy.create(req.body);
    
    res.status(201).json({
      success: true,
      data: therapy
    });
  } catch (error) {
    console.error('Create therapy error:', error);
    if (error.code === 11000) {
      return res.status(400).json({ error: 'Therapy with this code already exists' });
    }
    res.status(500).json({ error: 'Server error' });
  }
};

// @desc    Update therapy
// @route   PUT /api/therapies/:id
// @access  Admin only
const updateTherapy = async (req, res) => {
  try {
    const therapy = await Therapy.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );

    if (!therapy) {
      return res.status(404).json({ error: 'Therapy not found' });
    }

    res.json({
      success: true,
      data: therapy
    });
  } catch (error) {
    console.error('Update therapy error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

// @desc    Delete therapy (soft delete)
// @route   DELETE /api/therapies/:id
// @access  Admin only
const deleteTherapy = async (req, res) => {
  try {
    const therapy = await Therapy.findByIdAndUpdate(
      req.params.id,
      { isActive: false },
      { new: true }
    );

    if (!therapy) {
      return res.status(404).json({ error: 'Therapy not found' });
    }

    res.json({
      success: true,
      message: 'Therapy deactivated successfully'
    });
  } catch (error) {
    console.error('Delete therapy error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

// @desc    Get therapy categories
// @route   GET /api/therapies/categories
// @access  Public
const getTherapyCategories = async (req, res) => {
  try {
    const categories = await Therapy.distinct('category', { isActive: true });
    
    res.json({
      success: true,
      data: categories
    });
  } catch (error) {
    console.error('Get therapy categories error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

module.exports = {
  getTherapies,
  getTherapy,
  createTherapy,
  updateTherapy,
  deleteTherapy,
  getTherapyCategories
};
