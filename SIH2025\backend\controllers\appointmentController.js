const Appointment = require('../models/Appointment');
const Protocol = require('../models/Protocol');
const Therapist = require('../models/Therapist');
const Room = require('../models/Room');
const Patient = require('../models/Patient');
const Therapy = require('../models/Therapy');
const mongoose = require('mongoose');

// @desc    Get all appointments
// @route   GET /api/appointments
// @access  Admin, Therapist (own), Patient (own)
const getAppointments = async (req, res) => {
  try {
    const { patient, therapist, status, startDate, endDate } = req.query;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const query = {};

    // Role-based filtering
    if (req.user.role === 'patient') {
      query.patient = req.user._id;
    } else if (req.user.role === 'therapist') {
      query.therapist = req.user._id;
    }

    // Additional filters
    if (patient && req.user.role === 'admin') {
      query.patient = patient;
    }
    if (therapist && (req.user.role === 'admin' || req.user.role === 'patient')) {
      query.therapist = therapist;
    }
    if (status) {
      query.status = status;
    }
    if (startDate || endDate) {
      query.scheduledAt = {};
      if (startDate) query.scheduledAt.$gte = new Date(startDate);
      if (endDate) query.scheduledAt.$lte = new Date(endDate);
    }

    const appointments = await Appointment.find(query)
      .populate('patient', 'name phone email')
      .populate('therapist', 'name specialization')
      .populate('therapy', 'name duration')
      .populate('room', 'name roomNumber')
      .skip(skip)
      .limit(limit)
      .sort({ scheduledAt: -1 });

    const total = await Appointment.countDocuments(query);

    res.json({
      success: true,
      data: appointments,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get appointments error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

// @desc    Get single appointment
// @route   GET /api/appointments/:id
// @access  Admin, Therapist (own), Patient (own)
const getAppointment = async (req, res) => {
  try {
    const appointment = await Appointment.findById(req.params.id)
      .populate('patient', 'name phone email medicalHistory allergies')
      .populate('therapist', 'name specialization phone')
      .populate('therapy', 'name description duration preparationInstructions postTreatmentCare')
      .populate('protocol', 'name description')
      .populate('room', 'name roomNumber type facilities');

    if (!appointment) {
      return res.status(404).json({ error: 'Appointment not found' });
    }

    // Check authorization
    if (req.user.role === 'patient' && appointment.patient._id.toString() !== req.user._id.toString()) {
      return res.status(403).json({ error: 'Not authorized to view this appointment' });
    }
    if (req.user.role === 'therapist' && appointment.therapist._id.toString() !== req.user._id.toString()) {
      return res.status(403).json({ error: 'Not authorized to view this appointment' });
    }

    res.json({
      success: true,
      data: appointment
    });
  } catch (error) {
    console.error('Get appointment error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

// @desc    Auto-schedule sessions when patient enrolls in protocol
// @route   POST /api/appointments/enroll
// @access  Admin, Patient
const enrollPatient = async (req, res) => {
  try {
    const { patientId, protocolId, startDate, preferredTherapist } = req.body;

    // Validate inputs
    if (!patientId || !protocolId || !startDate) {
      return res.status(400).json({ error: 'Patient ID, Protocol ID, and start date are required' });
    }

    // Verify patient exists
    const patient = await Patient.findById(patientId);
    if (!patient) {
      return res.status(404).json({ error: 'Patient not found' });
    }

    // Verify protocol exists and populate therapy
    const protocol = await Protocol.findById(protocolId).populate('therapy');
    if (!protocol) {
      return res.status(404).json({ error: 'Protocol not found' });
    }

    let currentDate = new Date(startDate);
    const createdAppointments = [];

    for (const step of protocol.steps) {
      // Find available therapist
      let therapist;
      if (preferredTherapist) {
        therapist = await Therapist.findById(preferredTherapist);
      }
      if (!therapist) {
        therapist = await Therapist.findOne({ 
          isActive: true,
          therapiesOffered: protocol.therapy._id
        });
      }
      if (!therapist) {
        therapist = await Therapist.findOne({ isActive: true });
      }

      // Find available room
      const room = await Room.findOne({ 
        isActive: true,
        type: { $in: ['Treatment Room', 'Panchakarma Suite', 'Massage Room'] }
      });

      const scheduledAt = new Date(currentDate);
      
      // Set time to 9 AM by default
      scheduledAt.setHours(9, 0, 0, 0);

      const appointment = await Appointment.create({
        patient: patientId,
        therapist: therapist?._id,
        therapy: protocol.therapy._id,
        protocol: protocolId,
        protocolStep: step.sessionType,
        stepNumber: step.stepNumber,
        scheduledAt,
        duration: step.duration,
        status: 'scheduled',
        room: room?._id,
        preInstructions: step.preInstructions,
        postInstructions: step.postInstructions,
        price: protocol.therapy.price || 0
      });

      await appointment.populate('therapist', 'name specialization');
      await appointment.populate('therapy', 'name');
      await appointment.populate('room', 'name roomNumber');

      createdAppointments.push(appointment);
      
      // Move to next session date
      currentDate.setDate(currentDate.getDate() + step.gapDays);
    }

    res.json({
      success: true,
      message: `${createdAppointments.length} appointments scheduled successfully`,
      data: {
        protocol: protocol.name,
        therapy: protocol.therapy.name,
        appointments: createdAppointments
      }
    });
  } catch (error) {
    console.error('Enroll patient error:', error);
    res.status(500).json({ error: 'Server error during enrollment' });
  }
};

// @desc    Create single appointment
// @route   POST /api/appointments
// @access  Admin, Patient
const createAppointment = async (req, res) => {
  try {
    const { patientId, therapistId, therapyId, scheduledAt, duration } = req.body;

    // Validate required fields
    if (!patientId || !therapistId || !therapyId || !scheduledAt || !duration) {
      return res.status(400).json({ error: 'All fields are required' });
    }

    // Verify entities exist
    const [patient, therapist, therapy] = await Promise.all([
      Patient.findById(patientId),
      Therapist.findById(therapistId),
      Therapy.findById(therapyId)
    ]);

    if (!patient) return res.status(404).json({ error: 'Patient not found' });
    if (!therapist) return res.status(404).json({ error: 'Therapist not found' });
    if (!therapy) return res.status(404).json({ error: 'Therapy not found' });

    // Check for conflicts
    const conflictingAppointment = await Appointment.findOne({
      therapist: therapistId,
      scheduledAt: {
        $gte: new Date(scheduledAt),
        $lt: new Date(new Date(scheduledAt).getTime() + duration * 60000)
      },
      status: { $in: ['scheduled', 'confirmed', 'in-progress'] }
    });

    if (conflictingAppointment) {
      return res.status(400).json({ error: 'Therapist is not available at this time' });
    }

    // Find available room
    const room = await Room.findOne({
      isActive: true,
      type: { $in: ['Treatment Room', 'Panchakarma Suite', 'Massage Room'] }
    });

    const appointment = await Appointment.create({
      patient: patientId,
      therapist: therapistId,
      therapy: therapyId,
      protocolStep: 'Single Session',
      stepNumber: 1,
      scheduledAt: new Date(scheduledAt),
      duration,
      room: room?._id,
      price: therapy.price || 0,
      ...req.body
    });

    await appointment.populate('patient', 'name phone email');
    await appointment.populate('therapist', 'name specialization');
    await appointment.populate('therapy', 'name duration');
    await appointment.populate('room', 'name roomNumber');

    res.status(201).json({
      success: true,
      data: appointment
    });
  } catch (error) {
    console.error('Create appointment error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

// @desc    Update appointment
// @route   PUT /api/appointments/:id
// @access  Admin, Therapist (own), Patient (own - limited fields)
const updateAppointment = async (req, res) => {
  try {
    const appointment = await Appointment.findById(req.params.id);

    if (!appointment) {
      return res.status(404).json({ error: 'Appointment not found' });
    }

    // Check authorization
    if (req.user.role === 'patient' && appointment.patient.toString() !== req.user._id.toString()) {
      return res.status(403).json({ error: 'Not authorized to update this appointment' });
    }
    if (req.user.role === 'therapist' && appointment.therapist.toString() !== req.user._id.toString()) {
      return res.status(403).json({ error: 'Not authorized to update this appointment' });
    }

    // Define allowed fields based on role
    let allowedFields = [];
    if (req.user.role === 'admin') {
      allowedFields = ['scheduledAt', 'duration', 'status', 'therapist', 'room', 'notes', 'price', 'paymentStatus'];
    } else if (req.user.role === 'therapist') {
      allowedFields = ['status', 'notes.therapistNotes', 'actualStartTime', 'actualEndTime', 'actualDuration'];
    } else if (req.user.role === 'patient') {
      allowedFields = ['notes.patientNotes'];
    }

    const updateData = {};
    allowedFields.forEach(field => {
      if (field.includes('.')) {
        const [parent, child] = field.split('.');
        if (req.body[parent] && req.body[parent][child] !== undefined) {
          if (!updateData[parent]) updateData[parent] = {};
          updateData[parent][child] = req.body[parent][child];
        }
      } else if (req.body[field] !== undefined) {
        updateData[field] = req.body[field];
      }
    });

    const updatedAppointment = await Appointment.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    )
    .populate('patient', 'name phone email')
    .populate('therapist', 'name specialization')
    .populate('therapy', 'name duration')
    .populate('room', 'name roomNumber');

    res.json({
      success: true,
      data: updatedAppointment
    });
  } catch (error) {
    console.error('Update appointment error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

// @desc    Cancel appointment
// @route   PUT /api/appointments/:id/cancel
// @access  Admin, Therapist (own), Patient (own)
const cancelAppointment = async (req, res) => {
  try {
    const { cancellationReason } = req.body;

    const appointment = await Appointment.findById(req.params.id);

    if (!appointment) {
      return res.status(404).json({ error: 'Appointment not found' });
    }

    // Check authorization
    if (req.user.role === 'patient' && appointment.patient.toString() !== req.user._id.toString()) {
      return res.status(403).json({ error: 'Not authorized to cancel this appointment' });
    }
    if (req.user.role === 'therapist' && appointment.therapist.toString() !== req.user._id.toString()) {
      return res.status(403).json({ error: 'Not authorized to cancel this appointment' });
    }

    const updatedAppointment = await Appointment.findByIdAndUpdate(
      req.params.id,
      {
        status: 'cancelled',
        cancellationReason: cancellationReason || 'No reason provided'
      },
      { new: true }
    )
    .populate('patient', 'name phone email')
    .populate('therapist', 'name specialization')
    .populate('therapy', 'name duration');

    res.json({
      success: true,
      message: 'Appointment cancelled successfully',
      data: updatedAppointment
    });
  } catch (error) {
    console.error('Cancel appointment error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

module.exports = {
  getAppointments,
  getAppointment,
  enrollPatient,
  createAppointment,
  updateAppointment,
  cancelAppointment
};
