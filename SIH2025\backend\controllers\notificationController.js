const Notification = require('../models/Notification');
const Appointment = require('../models/Appointment');
const Patient = require('../models/Patient');
const Therapist = require('../models/Therapist');

// @desc    Get notifications for user
// @route   GET /api/notifications
// @access  Private
const getNotifications = async (req, res) => {
  try {
    const { page = 1, limit = 20, isRead, type } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);

    const query = {
      recipient: req.user._id,
      recipientModel: req.user.role === 'patient' ? 'Patient' : 'Therapist'
    };

    if (isRead !== undefined) {
      query.isRead = isRead === 'true';
    }

    if (type) {
      query.type = type;
    }

    // Don't show expired notifications
    query.$or = [
      { expiresAt: { $exists: false } },
      { expiresAt: { $gt: new Date() } }
    ];

    const notifications = await Notification.find(query)
      .populate('appointment', 'scheduledAt protocolStep')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Notification.countDocuments(query);
    const unreadCount = await Notification.getUnreadCount(req.user._id, query.recipientModel);

    res.json({
      success: true,
      data: notifications,
      unreadCount,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    console.error('Get notifications error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

// @desc    Mark notification as read
// @route   PUT /api/notifications/:id/read
// @access  Private
const markAsRead = async (req, res) => {
  try {
    const notification = await Notification.findById(req.params.id);

    if (!notification) {
      return res.status(404).json({ error: 'Notification not found' });
    }

    // Check if user owns the notification
    if (notification.recipient.toString() !== req.user._id.toString()) {
      return res.status(403).json({ error: 'Not authorized to update this notification' });
    }

    await notification.markAsRead();

    res.json({
      success: true,
      message: 'Notification marked as read'
    });
  } catch (error) {
    console.error('Mark notification as read error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

// @desc    Mark all notifications as read
// @route   PUT /api/notifications/read-all
// @access  Private
const markAllAsRead = async (req, res) => {
  try {
    await Notification.updateMany(
      {
        recipient: req.user._id,
        recipientModel: req.user.role === 'patient' ? 'Patient' : 'Therapist',
        isRead: false
      },
      {
        isRead: true,
        readAt: new Date(),
        status: 'read'
      }
    );

    res.json({
      success: true,
      message: 'All notifications marked as read'
    });
  } catch (error) {
    console.error('Mark all notifications as read error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

// @desc    Create notification (Admin only)
// @route   POST /api/notifications
// @access  Admin
const createNotification = async (req, res) => {
  try {
    const notification = await Notification.create(req.body);
    
    res.status(201).json({
      success: true,
      data: notification
    });
  } catch (error) {
    console.error('Create notification error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

// Helper function to create appointment reminder notifications
const createAppointmentReminder = async (appointment, reminderType = 'appointment_reminder') => {
  try {
    const patient = await Patient.findById(appointment.patient);
    const therapist = await Therapist.findById(appointment.therapist);
    
    if (!patient || !therapist) return;

    const appointmentDate = new Date(appointment.scheduledAt);
    const formattedDate = appointmentDate.toLocaleDateString();
    const formattedTime = appointmentDate.toLocaleTimeString();

    // Create notification for patient
    if (patient.notificationPrefs.inApp) {
      await Notification.create({
        recipient: patient._id,
        recipientModel: 'Patient',
        appointment: appointment._id,
        type: reminderType,
        channel: 'inApp',
        title: 'Appointment Reminder',
        message: `Your ${appointment.protocolStep} session with ${therapist.name} is scheduled for ${formattedDate} at ${formattedTime}`,
        templateId: 'appointment_reminder',
        templateData: {
          patientName: patient.name,
          therapistName: therapist.name,
          sessionType: appointment.protocolStep,
          date: formattedDate,
          time: formattedTime
        },
        priority: 'medium',
        expiresAt: new Date(appointmentDate.getTime() + 24 * 60 * 60 * 1000) // Expire 24 hours after appointment
      });
    }

    // Simulate SMS/Email notifications
    if (patient.notificationPrefs.sms || patient.notificationPrefs.email) {
      const message = `Reminder: Your ${appointment.protocolStep} session with ${therapist.name} is scheduled for ${formattedDate} at ${formattedTime}. Please arrive 15 minutes early.`;
      
      if (patient.notificationPrefs.email) {
        console.log(`📧 EMAIL to ${patient.email}: ${message}`);
      }
      
      if (patient.notificationPrefs.sms) {
        console.log(`📱 SMS to ${patient.phone}: ${message}`);
      }
    }

    // Create notification for therapist
    await Notification.create({
      recipient: therapist._id,
      recipientModel: 'Therapist',
      appointment: appointment._id,
      type: reminderType,
      channel: 'inApp',
      title: 'Upcoming Session',
      message: `You have a ${appointment.protocolStep} session with ${patient.name} scheduled for ${formattedDate} at ${formattedTime}`,
      templateId: 'therapist_reminder',
      templateData: {
        patientName: patient.name,
        therapistName: therapist.name,
        sessionType: appointment.protocolStep,
        date: formattedDate,
        time: formattedTime
      },
      priority: 'medium',
      expiresAt: new Date(appointmentDate.getTime() + 24 * 60 * 60 * 1000)
    });

  } catch (error) {
    console.error('Create appointment reminder error:', error);
  }
};

// Background job: Check for upcoming appointments and send reminders
const checkUpcomingAppointments = async () => {
  try {
    const now = new Date();
    const oneHourFromNow = new Date(now.getTime() + 60 * 60 * 1000);
    const oneDayFromNow = new Date(now.getTime() + 24 * 60 * 60 * 1000);

    // Find appointments in the next hour (for immediate reminders)
    const upcomingAppointments = await Appointment.find({
      scheduledAt: { $gte: now, $lte: oneHourFromNow },
      status: { $in: ['scheduled', 'confirmed'] }
    }).populate('patient').populate('therapist');

    // Find appointments in the next 24 hours (for day-ahead reminders)
    const tomorrowAppointments = await Appointment.find({
      scheduledAt: { $gte: oneHourFromNow, $lte: oneDayFromNow },
      status: { $in: ['scheduled', 'confirmed'] }
    }).populate('patient').populate('therapist');

    // Send immediate reminders (1 hour before)
    for (const appointment of upcomingAppointments) {
      // Check if reminder already sent
      const existingReminder = await Notification.findOne({
        appointment: appointment._id,
        type: 'appointment_reminder',
        createdAt: { $gte: new Date(now.getTime() - 60 * 60 * 1000) }
      });

      if (!existingReminder) {
        await createAppointmentReminder(appointment, 'appointment_reminder');
      }
    }

    // Send day-ahead reminders
    for (const appointment of tomorrowAppointments) {
      // Check if day-ahead reminder already sent
      const existingReminder = await Notification.findOne({
        appointment: appointment._id,
        type: 'appointment_reminder',
        createdAt: { $gte: new Date(now.getTime() - 24 * 60 * 60 * 1000) }
      });

      if (!existingReminder) {
        await createAppointmentReminder(appointment, 'appointment_reminder');
      }
    }

    console.log(`✅ Notification check completed: ${upcomingAppointments.length} immediate reminders, ${tomorrowAppointments.length} day-ahead reminders`);
  } catch (error) {
    console.error('❌ Background notification job error:', error);
  }
};

// Start the background job (runs every 15 minutes)
console.log('🔄 Starting notification background job...');
setInterval(checkUpcomingAppointments, 15 * 60 * 1000); // Every 15 minutes

// Run once on startup
setTimeout(checkUpcomingAppointments, 5000); // Wait 5 seconds after startup

module.exports = {
  getNotifications,
  markAsRead,
  markAllAsRead,
  createNotification,
  createAppointmentReminder,
  checkUpcomingAppointments
};
