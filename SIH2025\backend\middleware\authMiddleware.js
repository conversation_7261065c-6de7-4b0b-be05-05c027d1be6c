const jwt = require('jsonwebtoken');
const Patient = require('../models/Patient');
const Therapist = require('../models/Therapist');

// Generate JWT token
const generateToken = (id, role) => {
  return jwt.sign({ id, role }, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRE || '30d',
  });
};

// Protect routes - verify JWT token
const protect = async (req, res, next) => {
  let token;

  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    try {
      // Get token from header
      token = req.headers.authorization.split(' ')[1];

      // Verify token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);

      // Get user from token
      let user;
      if (decoded.role === 'patient' || decoded.role === 'admin') {
        user = await Patient.findById(decoded.id).select('-password');
      } else if (decoded.role === 'therapist') {
        user = await Therapist.findById(decoded.id).select('-password');
      }

      if (!user) {
        return res.status(401).json({ error: 'Not authorized, user not found' });
      }

      req.user = user;
      next();
    } catch (error) {
      console.error('Token verification error:', error);
      return res.status(401).json({ error: 'Not authorized, token failed' });
    }
  }

  if (!token) {
    return res.status(401).json({ error: 'Not authorized, no token' });
  }
};

// Admin only access
const adminOnly = (req, res, next) => {
  if (req.user && req.user.role === 'admin') {
    next();
  } else {
    res.status(403).json({ error: 'Access denied. Admin only.' });
  }
};

// Therapist or Admin access
const therapistOrAdmin = (req, res, next) => {
  if (req.user && (req.user.role === 'therapist' || req.user.role === 'admin')) {
    next();
  } else {
    res.status(403).json({ error: 'Access denied. Therapist or Admin only.' });
  }
};

// Patient or Admin access
const patientOrAdmin = (req, res, next) => {
  if (req.user && (req.user.role === 'patient' || req.user.role === 'admin')) {
    next();
  } else {
    res.status(403).json({ error: 'Access denied. Patient or Admin only.' });
  }
};

// Check if user owns the resource or is admin
const ownerOrAdmin = (resourceField = 'patient') => {
  return (req, res, next) => {
    if (req.user.role === 'admin') {
      return next();
    }

    // For patients, check if they own the resource
    if (req.user.role === 'patient') {
      const resourceId = req.params.id || req.body[resourceField] || req.query[resourceField];
      if (req.user._id.toString() === resourceId) {
        return next();
      }
    }

    // For therapists, check if they are assigned to the resource
    if (req.user.role === 'therapist') {
      const therapistId = req.body.therapist || req.query.therapist;
      if (req.user._id.toString() === therapistId) {
        return next();
      }
    }

    res.status(403).json({ error: 'Access denied. Insufficient permissions.' });
  };
};

// Login function for patients, therapists, and admins
const login = async (req, res) => {
  try {
    const { email, password, userType } = req.body;

    if (!email || !password) {
      return res.status(400).json({ error: 'Please provide email and password' });
    }

    let user;
    let role;

    // Try to find user in both collections
    if (userType === 'therapist') {
      user = await Therapist.findOne({ email }).select('+password');
      role = user?.role || 'therapist';
    } else {
      // Check Patient collection (includes patients and admins)
      user = await Patient.findOne({ email }).select('+password');
      role = user?.role || 'patient';
    }

    // If not found in specified collection, try the other one
    if (!user) {
      if (userType === 'therapist') {
        user = await Patient.findOne({ email }).select('+password');
        role = user?.role || 'patient';
      } else {
        user = await Therapist.findOne({ email }).select('+password');
        role = user?.role || 'therapist';
      }
    }

    if (!user || !(await user.comparePassword(password))) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Generate token with the actual user role
    const token = generateToken(user._id, user.role || role);

    // Remove password from response
    user.password = undefined;

    res.json({
      success: true,
      token,
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        role: user.role || role,
        phone: user.phone
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Server error during login' });
  }
};

// Register function for patients
const registerPatient = async (req, res) => {
  try {
    const { name, email, phone, password } = req.body;

    if (!name || !email || !phone || !password) {
      return res.status(400).json({ error: 'Please provide all required fields' });
    }

    // Check if user already exists
    const existingPatient = await Patient.findOne({ email });
    if (existingPatient) {
      return res.status(400).json({ error: 'Patient already exists with this email' });
    }

    // Create patient
    const patient = await Patient.create({
      name,
      email,
      phone,
      password,
      role: 'patient'
    });

    // Generate token
    const token = generateToken(patient._id, 'patient');

    res.status(201).json({
      success: true,
      token,
      user: {
        id: patient._id,
        name: patient.name,
        email: patient.email,
        role: 'patient',
        phone: patient.phone
      }
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ error: 'Server error during registration' });
  }
};

module.exports = {
  generateToken,
  protect,
  adminOnly,
  therapistOrAdmin,
  patientOrAdmin,
  ownerOrAdmin,
  login,
  registerPatient
};
