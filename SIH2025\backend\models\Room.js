const mongoose = require('mongoose');

const roomSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Room name is required'],
    trim: true
  },
  roomNumber: {
    type: String,
    required: [true, 'Room number is required'],
    unique: true,
    trim: true
  },
  type: {
    type: String,
    required: [true, 'Room type is required'],
    enum: [
      'Treatment Room',
      'Consultation Room',
      'Panchakarma Suite',
      'Shirodhara Room',
      'Steam Room',
      'Massage Room',
      'Meditation Room',
      'Recovery Room'
    ]
  },
  capacity: {
    type: Number,
    required: [true, 'Room capacity is required'],
    min: 1,
    max: 10
  },
  facilities: [{
    type: String,
    trim: true
  }],
  equipment: [{
    name: String,
    quantity: Number,
    condition: {
      type: String,
      enum: ['Excellent', 'Good', 'Fair', 'Needs Repair'],
      default: 'Good'
    }
  }],
  location: {
    floor: {
      type: Number,
      required: [true, 'Floor is required']
    },
    wing: {
      type: String,
      enum: ['North', 'South', 'East', 'West', 'Central']
    },
    description: String
  },
  availability: [{
    day: {
      type: String,
      enum: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
    },
    startTime: String,
    endTime: String,
    isAvailable: {
      type: Boolean,
      default: true
    }
  }],
  maintenanceSchedule: [{
    date: Date,
    type: {
      type: String,
      enum: ['Cleaning', 'Deep Cleaning', 'Equipment Check', 'Repair', 'Renovation']
    },
    description: String,
    status: {
      type: String,
      enum: ['Scheduled', 'In Progress', 'Completed', 'Cancelled'],
      default: 'Scheduled'
    }
  }],
  isActive: {
    type: Boolean,
    default: true
  },
  images: [{
    type: String
  }],
  notes: {
    type: String,
    default: ''
  }
}, {
  timestamps: true
});

// Index for efficient queries
roomSchema.index({ type: 1, isActive: 1 });
roomSchema.index({ roomNumber: 1 });

// Method to check if room is available at a specific time
roomSchema.methods.isAvailableAt = function(date, duration) {
  // This would check against existing appointments
  // For now, we'll assume it's available if the room is active
  return this.isActive;
};

module.exports = mongoose.model('Room', roomSchema);
