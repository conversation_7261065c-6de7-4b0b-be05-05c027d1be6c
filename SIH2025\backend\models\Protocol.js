const mongoose = require('mongoose');

const protocolSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Protocol name is required'],
    trim: true
  },
  therapy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Therapy',
    required: [true, 'Therapy reference is required']
  },
  description: {
    type: String,
    required: [true, 'Protocol description is required']
  },
  totalDuration: {
    type: Number,
    required: [true, 'Total duration is required'],
    min: 1 // in days
  },
  steps: [{
    stepNumber: {
      type: Number,
      required: true
    },
    sessionType: {
      type: String,
      required: [true, 'Session type is required'],
      enum: [
        'Consultation',
        'Preparation',
        'Main Treatment',
        'Follow-up',
        'Assessment',
        'Rest Day'
      ]
    },
    duration: {
      type: Number,
      required: [true, 'Step duration is required'],
      min: 15,
      max: 240 // in minutes
    },
    gapDays: {
      type: Number,
      default: 1,
      min: 0,
      max: 30 // days before next session
    },
    preInstructions: {
      type: String,
      default: ''
    },
    postInstructions: {
      type: String,
      default: ''
    },
    materials: [{
      name: String,
      quantity: String,
      unit: String
    }],
    isOptional: {
      type: Boolean,
      default: false
    }
  }],
  targetConditions: [{
    type: String,
    trim: true
  }],
  contraindications: [{
    type: String,
    trim: true
  }],
  expectedOutcomes: [{
    type: String,
    trim: true
  }],
  difficulty: {
    type: String,
    enum: ['Beginner', 'Intermediate', 'Advanced'],
    default: 'Intermediate'
  },
  isActive: {
    type: Boolean,
    default: true
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Therapist'
  }
}, {
  timestamps: true
});

// Validate that steps are in correct order
protocolSchema.pre('save', function(next) {
  if (this.steps && this.steps.length > 0) {
    this.steps.sort((a, b) => a.stepNumber - b.stepNumber);
    
    // Ensure step numbers are sequential
    for (let i = 0; i < this.steps.length; i++) {
      this.steps[i].stepNumber = i + 1;
    }
  }
  next();
});

module.exports = mongoose.model('Protocol', protocolSchema);
